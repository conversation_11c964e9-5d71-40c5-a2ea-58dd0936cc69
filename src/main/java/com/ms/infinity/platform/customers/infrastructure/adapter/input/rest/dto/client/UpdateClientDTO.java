package com.ms.infinity.platform.customers.infrastructure.adapter.input.rest.dto.client;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * DTO para la actualización de un cliente existente.
 * Incluye validaciones para garantizar la integridad de los datos.
 */
public record UpdateClientDTO(
        @NotBlank(message = "El nombre de la empresa es obligatorio")
        @Size(max = 255, message = "El nombre de la empresa no puede superar los 255 caracteres")
        String companyName,

        @NotBlank(message = "El NIT/identificación tributaria es obligatorio")
        @Size(max = 50, message = "El NIT no puede superar los 50 caracteres")
        String taxId,

        @Size(max = 255, message = "La razón social no puede superar los 255 caracteres")
        String businessName,

        @Size(max = 50, message = "El dígito de verificación no puede superar los 50 caracteres")
        String verificationNumber,

        @Size(max = 255, message = "El nombre de contacto no puede superar los 255 caracteres")
        String contactName,

        @Size(max = 255, message = "La dirección no puede superar los 255 caracteres")
        String address,

        @Size(max = 10, message = "El indicativo de país no puede superar los 10 caracteres")
        String countryCode,

        @Size(max = 50, message = "El teléfono no puede superar los 50 caracteres")
        String phone,

        @NotBlank(message = "El correo electrónico es obligatorio")
        @Email(message = "El formato del correo electrónico no es válido")
        @Size(max = 255, message = "El correo electrónico no puede superar los 255 caracteres")
        String email,

        LocalDate birthDate,

        @NotNull(message = "La tasa de IVA es obligatoria")
        BigDecimal vatRate,

        @NotNull(message = "La tasa de impuesto al consumo es obligatoria")
        BigDecimal consumptionTaxRate,

        @NotNull(message = "El número de usuarios permitidos es obligatorio")
        Integer allowedUsers,

        @NotNull(message = "El estado del cliente es obligatorio")
        Boolean isActive
) {}