package com.ms.infinity.platform.customers.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Modelo de dominio para ingresos.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Income {
    private Integer incomeId;
    private Integer clientId;
    private Integer incomeTypeId;
    private Long userId;
    private LocalDateTime date;
    private BigDecimal amount;
    private String description;
    private LocalDateTime updateDate;
}