package com.ms.infinity.platform.customers.infrastructure.adapter.input.rest;

import com.ms.infinity.platform.customers.application.port.input.income.IncomeUseCase;
import com.ms.infinity.platform.customers.domain.model.Income;
import com.ms.infinity.platform.customers.infrastructure.adapter.input.rest.dto.income.CreateIncomeDTO;
import com.ms.infinity.platform.customers.infrastructure.adapter.input.rest.dto.income.UpdateIncomeDTO;
import com.ms.infinity.platform.customers.infrastructure.configuration.Constants;
import com.ms.infinity.platform.customers.infrastructure.exception.IncomeException;
import com.ms.infinity.platform.customers.infrastructure.security.annotation.RoleSecured;
import com.ms.infinity.platform.customers.infrastructure.util.HttpUtils;

import jakarta.annotation.security.PermitAll;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeParseException;
import java.util.Optional;

/**
 * Controlador REST para operaciones CRUD sobre ingresos.
 * Actúa como adaptador de entrada para el puerto IncomeUseCase.
 */
@Path("/api/incomes")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Income", description = "Income management operations")
@ApplicationScoped
public class IncomeResource {

    @Inject
    IncomeUseCase incomeUseCase;

    @Inject
    HttpUtils httpUtils;

    /**
     * Crea un nuevo ingreso en el sistema.
     *
     * @param dto Datos del ingreso a crear
     * @param token Token de autenticación
     * @return Ingreso creado
     */
    @POST
    @Operation(summary = "Create a new income")
    @RoleSecured("administrador")
    public Response createIncome(
            @Valid CreateIncomeDTO dto,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            Income income = new Income();
            income.setAmount(dto.amount());
            income.setIncomeTypeId(dto.incomeTypeId());
            income.setUserId(dto.userId());
            income.setClientId(dto.clientId());
            income.setDescription(dto.description());

            Income createdIncome = incomeUseCase.createIncome(income);
            return httpUtils.buildSuccessResponse(
                    createdIncome,
                    "Ingreso creado exitosamente",
                    Response.Status.CREATED.getStatusCode()
            );
        } catch (IncomeException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.VALIDATION,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    "Error interno al crear ingreso",
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Obtiene un ingreso por su ID.
     *
     * @param id ID del ingreso
     * @param token Token de autenticación
     * @return Ingreso solicitado o error si no existe
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "Get income by ID")
    @Transactional
    @PermitAll
    public Response getIncomeById(
            @PathParam("id") Integer id,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            return incomeUseCase.getIncomeById(id)
                    .map(income -> httpUtils.buildSuccessResponse(
                            income,
                            "Ingreso encontrado",
                            Response.Status.OK.getStatusCode()
                    ))
                    .orElseThrow(() -> new IncomeException("Ingreso no encontrado con ID: " + id));
        } catch (IncomeException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.NOT_FOUND,
                    e.getMessage(),
                    Response.Status.NOT_FOUND.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    "Error interno al buscar ingreso",
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Obtiene todos los ingresos del sistema.
     *
     * @param token Token de autenticación
     * @return Lista de ingresos
     */
    @GET
    @Operation(summary = "Get all incomes")
    @Transactional
    @RoleSecured("administrador")
    public Response getAllIncomes(
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            var incomes = incomeUseCase.getAllIncomes();
            return httpUtils.buildSuccessResponse(
                    incomes,
                    "Ingresos recuperados exitosamente",
                    Response.Status.OK.getStatusCode()
            );
        } catch (IncomeException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.RETRIEVING,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    "Error interno al listar ingresos",
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Busca ingresos por tipo.
     *
     * @param typeId ID del tipo de ingreso
     * @param token Token de autenticación
     * @return Lista de ingresos que coinciden con el criterio
     */
    @GET
    @Path("/type/{typeId}")
    @Operation(summary = "Search incomes by type")
    @Transactional
    @PermitAll
    public Response getIncomesByType(
            @PathParam("typeId") Integer typeId,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            var incomes = incomeUseCase.findIncomesByType(typeId);
            return httpUtils.buildSuccessResponse(
                    incomes,
                    "Ingresos recuperados exitosamente",
                    Response.Status.OK.getStatusCode()
            );
        } catch (IncomeException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.RETRIEVING,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    "Error interno al buscar ingresos por tipo",
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Busca ingresos por cliente.
     *
     * @param clientId ID del cliente
     * @param token Token de autenticación
     * @return Lista de ingresos que coinciden con el criterio
     */
    @GET
    @Path("/client/{clientId}")
    @Operation(summary = "Search incomes by client")
    @Transactional
    @PermitAll
    public Response getIncomesByClient(
            @PathParam("clientId") Integer clientId,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            var incomes = incomeUseCase.findIncomesByClient(clientId);
            return httpUtils.buildSuccessResponse(
                    incomes,
                    "Ingresos recuperados exitosamente",
                    Response.Status.OK.getStatusCode()
            );
        } catch (IncomeException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.RETRIEVING,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    "Error interno al buscar ingresos por cliente",
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Actualiza los datos de un ingreso existente.
     *
     * @param id ID del ingreso a actualizar
     * @param dto Nuevos datos del ingreso
     * @param token Token de autenticación
     * @return Ingreso actualizado
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "Update an existing income")
    @Transactional
    @RoleSecured("administrador")
    public Response updateIncome(
            @PathParam("id") Integer id,
            @Valid UpdateIncomeDTO dto,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            // Primero verificar que el ingreso existe
            Optional<Income> existingIncome = incomeUseCase.getIncomeById(id);
            if (existingIncome.isEmpty()) {
                throw new IncomeException("Ingreso no encontrado con ID: " + id);
            }

            Income income = new Income();
            income.setIncomeId(id);
            income.setAmount(dto.amount());
            income.setIncomeTypeId(dto.incomeTypeId());
            income.setUserId(dto.userId());
            income.setClientId(dto.clientId());
            income.setDescription(dto.description());
            // La fecha de creación (date) no se modifica en actualizaciones
            // El updateDate se establece automáticamente en el servicio

            Income updatedIncome = incomeUseCase.updateIncome(id, income);
            return httpUtils.buildSuccessResponse(
                    updatedIncome,
                    "Ingreso actualizado exitosamente",
                    Response.Status.OK.getStatusCode()
            );
        } catch (IncomeException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.VALIDATION,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    "Error interno al actualizar ingreso",
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Elimina un ingreso del sistema.
     *
     * @param id ID del ingreso a eliminar
     * @param token Token de autenticación
     * @return Confirmación de la operación
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "Delete an income")
    @RoleSecured("administrador")
    public Response deleteIncome(
            @PathParam("id") Integer id,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            incomeUseCase.deleteIncome(id);
            return httpUtils.buildSuccessResponse(
                    null,
                    "Ingreso eliminado exitosamente",
                    Response.Status.OK.getStatusCode()
            );
        } catch (IncomeException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.NOT_FOUND,
                    e.getMessage(),
                    Response.Status.NOT_FOUND.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    "Error interno al eliminar ingreso",
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Busca ingresos por rango de fechas.
     *
     * @param startDateStr Fecha de inicio en formato yyyy-MM-dd
     * @param endDateStr Fecha de fin en formato yyyy-MM-dd
     * @param token Token de autenticación
     * @return Lista de ingresos que coinciden con el criterio de fechas
     */
    @GET
    @Path("/date-range")
    @Operation(summary = "Get incomes by date range")
    @Transactional
    @RoleSecured("administrador")
    public Response getIncomesByDateRange(
            @QueryParam("startDate")
            @Parameter(description = "Start date in format yyyy-MM-dd", required = true)
            String startDateStr,
            @QueryParam("endDate")
            @Parameter(description = "End date in format yyyy-MM-dd", required = true)
            String endDateStr,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            LocalDate startDate = LocalDate.parse(startDateStr);
            LocalDate endDate = LocalDate.parse(endDateStr);

            if (endDate.isBefore(startDate)) {
                throw new IncomeException("La fecha final no puede ser anterior a la fecha inicial");
            }

            var incomes = incomeUseCase.findIncomesByDateRange(startDate, endDate);
            return httpUtils.buildSuccessResponse(
                    incomes,
                    "Ingresos recuperados exitosamente",
                    Response.Status.OK.getStatusCode()
            );
        } catch (DateTimeParseException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.VALIDATION,
                    "Formato de fecha inválido. Use yyyy-MM-dd",
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (IncomeException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.RETRIEVING,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    "Error interno al buscar ingresos por rango de fechas: " + e.getMessage(),
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Obtiene todos los ingresos del mes actual.
     *
     * @param token Token de autenticación
     * @return Lista de ingresos del mes en curso
     */
    @GET
    @Path("/current-month")
    @Operation(summary = "Get incomes for the current month")
    @Transactional
    @RoleSecured("administrador")
    public Response getCurrentMonthIncomes(
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            // Obtener el primer y último día del mes actual
            YearMonth currentMonth = YearMonth.now();
            LocalDate firstDay = currentMonth.atDay(1);
            LocalDate lastDay = currentMonth.atEndOfMonth();

            var incomes = incomeUseCase.findIncomesByDateRange(firstDay, lastDay);
            return httpUtils.buildSuccessResponse(
                    incomes,
                    "Ingresos del mes actual recuperados exitosamente",
                    Response.Status.OK.getStatusCode()
            );
        } catch (IncomeException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.RETRIEVING,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    "Error interno al buscar ingresos del mes actual: " + e.getMessage(),
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }
}