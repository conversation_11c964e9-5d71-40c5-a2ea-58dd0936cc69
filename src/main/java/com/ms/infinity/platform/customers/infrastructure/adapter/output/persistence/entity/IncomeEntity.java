package com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entidad JPA para la tabla incomes.
 * Utiliza Panache para facilitar las operaciones de persistencia.
 */
@Entity
@Table(name = "incomes")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncomeEntity extends PanacheEntityBase {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "income_id")
    private Integer incomeId;

    @ManyToOne
    @JoinColumn(name = "client_id")
    private ClientEntity client;

    @ManyToOne
    @JoinColumn(name = "income_type_id", nullable = false)
    private IncomeTypeEntity incomeType;

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private UserEntity user;

    @Column(name = "date", nullable = false)
    private LocalDateTime date;

    @Column(name = "amount", nullable = false)
    private BigDecimal amount;

    @Column(name = "description")
    private String description;

    @Column(name = "updateDate")
    private LocalDateTime updateDate;

    /**
     * Método de consulta para encontrar ingresos por ID de tipo.
     *
     * @param typeId ID del tipo de ingreso
     * @return Lista de ingresos del tipo especificado
     */
    public static java.util.List<IncomeEntity> findByTypeId(Integer typeId) {
        return list("incomeType.incomeTypeId", typeId);
    }

    /**
     * Método de consulta para encontrar ingresos por ID de cliente.
     *
     * @param clientId ID del cliente
     * @return Lista de ingresos del cliente
     */
    public static java.util.List<IncomeEntity> findByClientId(Integer clientId) {
        return list("client.clientId", clientId);
    }

    /**
     * Método de consulta para encontrar ingresos por ID de usuario.
     *
     * @param userId ID del usuario
     * @return Lista de ingresos del usuario
     */
    public static java.util.List<IncomeEntity> findByUserId(Long userId) {
        return list("user.id", userId);
    }
}