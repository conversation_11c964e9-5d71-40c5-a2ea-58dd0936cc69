package com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence;

import com.ms.infinity.platform.customers.domain.model.Income;
import com.ms.infinity.platform.customers.application.port.output.IncomeRepository;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity.ClientEntity;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity.IncomeEntity;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity.IncomeTypeEntity;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity.UserEntity;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.mapper.IncomeMapper;
import com.ms.infinity.platform.customers.infrastructure.exception.IncomeException;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adaptador para el repositorio de ingresos.
 * Implementa el puerto de salida IncomeRepository.
 */
@Slf4j
@ApplicationScoped
public class IncomeRepositoryAdapter implements IncomeRepository {

    @Inject
    IncomeMapper mapper;

    @Override
    @Transactional
    public Income save(Income income) {
        log.debug("Guardando nuevo ingreso: {}", income);

        IncomeEntity entity = mapper.toEntity(income);

        // Obtener el tipo de ingreso
        IncomeTypeEntity typeEntity = IncomeTypeEntity.findById(income.getIncomeTypeId());
        if (typeEntity == null) {
            throw new IncomeException("Tipo de ingreso no encontrado con ID: " + income.getIncomeTypeId());
        }
        entity.setIncomeType(typeEntity);

        // Obtener el usuario
        UserEntity userEntity = UserEntity.findById(income.getUserId());
        if (userEntity == null) {
            throw new IncomeException("Usuario no encontrado con ID: " + income.getUserId());
        }
        entity.setUser(userEntity);

        // Obtener el cliente si existe
        if (income.getClientId() != null) {
            ClientEntity clientEntity = ClientEntity.findById(income.getClientId());
            if (clientEntity == null) {
                throw new IncomeException("Cliente no encontrado con ID: " + income.getClientId());
            }
            entity.setClient(clientEntity);
        }

        entity.persist();
        return mapper.toDomain(entity);
    }

    @Override
    public Optional<Income> findById(Integer id) {
        log.debug("Buscando ingreso con ID: {}", id);

        IncomeEntity entity = IncomeEntity.findById(id);
        return entity != null ? Optional.of(mapper.toDomain(entity)) : Optional.empty();
    }

    @Override
    public List<Income> findAll() {
        log.debug("Obteniendo todos los ingresos");

        List<IncomeEntity> entities = IncomeEntity.listAll();
        return entities.stream()
                .map(mapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<Income> findByTypeId(Integer typeId) {
        log.debug("Buscando ingresos por tipo con ID: {}", typeId);

        List<IncomeEntity> entities = IncomeEntity.findByTypeId(typeId);
        return entities.stream()
                .map(mapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<Income> findByClientId(Integer clientId) {
        log.debug("Buscando ingresos por cliente con ID: {}", clientId);

        List<IncomeEntity> entities = IncomeEntity.findByClientId(clientId);
        return entities.stream()
                .map(mapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Income update(Income income) {
        log.debug("Actualizando ingreso con ID: {}", income.getIncomeId());

        IncomeEntity entity = IncomeEntity.findById(income.getIncomeId());
        if (entity == null) {
            throw new IncomeException("Ingreso no encontrado con ID: " + income.getIncomeId());
        }

        // Actualizar campos
        entity.setAmount(income.getAmount());
        entity.setDescription(income.getDescription());
        entity.setDate(income.getDate());
        entity.setUpdateDate(income.getUpdateDate());

        // Actualizar tipo si ha cambiado
        if (!entity.getIncomeType().getIncomeTypeId().equals(income.getIncomeTypeId())) {
            IncomeTypeEntity typeEntity = IncomeTypeEntity.findById(income.getIncomeTypeId());
            if (typeEntity == null) {
                throw new IncomeException("Tipo de ingreso no encontrado con ID: " + income.getIncomeTypeId());
            }
            entity.setIncomeType(typeEntity);
        }

        // Actualizar usuario si ha cambiado
        if (!entity.getUser().getId().equals(income.getUserId())) {
            UserEntity userEntity = UserEntity.findById(income.getUserId());
            if (userEntity == null) {
                throw new IncomeException("Usuario no encontrado con ID: " + income.getUserId());
            }
            entity.setUser(userEntity);
        }

        // Actualizar cliente si ha cambiado
        if ((entity.getClient() == null && income.getClientId() != null) ||
            (entity.getClient() != null && !entity.getClient().getClientId().equals(income.getClientId()))) {

            if (income.getClientId() == null) {
                entity.setClient(null);
            } else {
                ClientEntity clientEntity = ClientEntity.findById(income.getClientId());
                if (clientEntity == null) {
                    throw new IncomeException("Cliente no encontrado con ID: " + income.getClientId());
                }
                entity.setClient(clientEntity);
            }
        }

        entity.persist();
        return mapper.toDomain(entity);
    }

    @Override
    @Transactional
    public void delete(Integer id) {
        log.debug("Eliminando ingreso con ID: {}", id);

        IncomeEntity entity = IncomeEntity.findById(id);
        if (entity == null) {
            throw new IncomeException("Ingreso no encontrado con ID: " + id);
        }

        entity.delete();
    }

    @Override
    public List<Income> findByUserId(Long userId) {
        log.debug("Buscando ingresos por usuario con ID: {}", userId);

        List<IncomeEntity> entities = IncomeEntity.findByUserId(userId);
        return entities.stream()
                .map(mapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<Income> findByDateRange(LocalDate startDate, LocalDate endDate) {
        log.debug("Buscando ingresos entre fechas: {} y {}", startDate, endDate);

        // Convertir LocalDate a LocalDateTime para incluir todo el día
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59, 999999999);

        // Usar una consulta explícita con el nombre correcto del campo
        String query = "FROM IncomeEntity i WHERE i.date >= :startDate AND i.date <= :endDate";
        List<IncomeEntity> entities = IncomeEntity.getEntityManager()
                .createQuery(query, IncomeEntity.class)
                .setParameter("startDate", startDateTime)
                .setParameter("endDate", endDateTime)
                .getResultList();

        return entities.stream()
                .map(mapper::toDomain)
                .collect(Collectors.toList());
    }
}