package com.ms.infinity.platform.customers.application.port.input.client;

import com.ms.infinity.platform.customers.domain.model.Client;

import java.util.List;
import java.util.Optional;

/**
 * Puerto de entrada para operaciones CRUD sobre clientes.
 * Define las operaciones que pueden realizarse sobre los clientes
 * desde la capa de aplicación hacia el dominio.
 */
public interface ClientUseCase {

    /**
     * Obtiene todos los clientes del sistema.
     *
     * @return Lista de todos los clientes
     */
    List<Client> getAllClients();

    /**
     * Obtiene un cliente por su ID.
     *
     * @param id Identificador único del cliente
     * @return Optional con el cliente si existe, Empty si no existe
     */
    Optional<Client> getClientById(Integer id);

    /**
     * Busca clientes por nombre de empresa.
     *
     * @param companyName Nombre o parte del nombre de la empresa
     * @return Lista de clientes que coinciden con el criterio de búsqueda
     */
    List<Client> searchClientsByCompanyName(String companyName);

    /**
     * Crea un nuevo cliente en el sistema.
     *
     * @param client Datos del cliente a crear
     * @return Cliente creado con ID asignado
     */
    Client createClient(Client client);

    /**
     * Actualiza los datos de un cliente existente.
     *
     * @param id ID del cliente a actualizar
     * @param client Nuevos datos del cliente
     * @return Cliente actualizado
     * @throws javax.persistence.EntityNotFoundException si el cliente no existe
     */
    Client updateClient(Integer id, Client client);



    /**
     * Elimina un cliente del sistema.
     *
     * @param id ID del cliente a eliminar
     * @throws javax.persistence.EntityNotFoundException si el cliente no existe
     */
    void deleteClient(Integer id);
}