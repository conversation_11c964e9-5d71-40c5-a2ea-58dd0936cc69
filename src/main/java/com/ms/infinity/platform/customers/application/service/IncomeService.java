package com.ms.infinity.platform.customers.application.service;

import com.ms.infinity.platform.customers.application.port.input.income.IncomeUseCase;
import com.ms.infinity.platform.customers.domain.model.Income;
import com.ms.infinity.platform.customers.application.port.output.IncomeRepository;
import com.ms.infinity.platform.customers.infrastructure.exception.IncomeException;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implementación de los casos de uso relacionados con ingresos.
 */
@Slf4j
@ApplicationScoped
public class IncomeService implements IncomeUseCase {

    @Inject
    IncomeRepository incomeRepository;

    @Override
    public Income createIncome(Income income) {
        log.debug("Creando nuevo ingreso: {}", income);

        // Validaciones
        if (income.getAmount() == null || income.getAmount().doubleValue() <= 0) {
            throw new IncomeException("El monto del ingreso debe ser mayor que cero");
        }

        if (income.getIncomeTypeId() == null) {
            throw new IncomeException("El tipo de ingreso es obligatorio");
        }

        if (income.getUserId() == null) {
            throw new IncomeException("El usuario es obligatorio");
        }

        // Establecer valores por defecto
        income.setDate(LocalDateTime.now());

        return incomeRepository.save(income);
    }

    @Override
    public Optional<Income> getIncomeById(Integer id) {
        log.debug("Buscando ingreso con ID: {}", id);
        return incomeRepository.findById(id);
    }

    @Override
    public List<Income> getAllIncomes() {
        log.debug("Obteniendo todos los ingresos");
        return incomeRepository.findAll();
    }

    @Override
    public List<Income> findIncomesByType(Integer typeId) {
        log.debug("Buscando ingresos por tipo con ID: {}", typeId);
        return incomeRepository.findByTypeId(typeId);
    }

    @Override
    public List<Income> findIncomesByClient(Integer clientId) {
        log.debug("Buscando ingresos por cliente con ID: {}", clientId);
        return incomeRepository.findByClientId(clientId);
    }

    @Override
    public Income updateIncome(Integer id, Income income) {
        log.debug("Actualizando ingreso con ID: {}", id);

        // Validar que el ingreso existe
        Optional<Income> existingIncome = incomeRepository.findById(id);
        if (existingIncome.isEmpty()) {
            throw new IncomeException("Ingreso no encontrado con ID: " + id);
        }

        // Validaciones
        if (income.getAmount() == null || income.getAmount().doubleValue() <= 0) {
            throw new IncomeException("El monto del ingreso debe ser mayor que cero");
        }

        if (income.getIncomeTypeId() == null) {
            throw new IncomeException("El tipo de ingreso es obligatorio");
        }

        // Asegurar que el ID es correcto
        income.setIncomeId(id);

        // Preservar la fecha de creación original (inmutable)
        income.setDate(existingIncome.get().getDate());

        // Establecer la fecha de actualización
        income.setUpdateDate(LocalDateTime.now());

        return incomeRepository.update(income);
    }

    @Override
    public void deleteIncome(Integer id) {
        log.debug("Eliminando ingreso con ID: {}", id);

        // Validar que el ingreso existe
        Optional<Income> existingIncome = incomeRepository.findById(id);
        if (existingIncome.isEmpty()) {
            throw new IncomeException("Ingreso no encontrado con ID: " + id);
        }

        incomeRepository.delete(id);
    }

    @Override
    public List<Income> findIncomesByDateRange(LocalDate startDate, LocalDate endDate) {
        log.debug("Buscando ingresos por rango de fechas: {} - {}", startDate, endDate);
        return incomeRepository.findByDateRange(startDate, endDate);
    }
}