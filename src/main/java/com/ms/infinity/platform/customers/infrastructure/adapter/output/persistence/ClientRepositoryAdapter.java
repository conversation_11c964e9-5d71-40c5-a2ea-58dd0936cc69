package com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence;

import com.ms.infinity.platform.customers.application.port.output.ClientRepository;
import com.ms.infinity.platform.customers.domain.model.Client;

import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity.ClientEntity;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity.UserEntity;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.mapper.ClientMapper;
import com.ms.infinity.platform.customers.infrastructure.exception.ClientException;
import com.ms.infinity.platform.customers.infrastructure.security.context.TenantContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * Implementación del puerto de salida ClientRepository utilizando Panache.
 * Adapta las operaciones de persistencia al modelo de dominio.
 */
@Slf4j
@ApplicationScoped
@RequiredArgsConstructor
public class ClientRepositoryAdapter implements ClientRepository {

    private final ClientMapper mapper;

    @Override
    public List<Client> findAll() {
        log.debug("Buscando todos los clientes en la base de datos");
        List<ClientEntity> entities = ClientEntity.listAll();
        return mapper.toDomainList(entities);
    }

    @Override
    public Optional<Client> findById(Integer id) {
        log.debug("Buscando cliente con ID: {}", id);
        return ClientEntity.findByIdOptional(id)
                .map(entity -> mapper.toDomain((ClientEntity) entity));
    }

    @Override
    public List<Client> findByCompanyNameContaining(String companyName) {
        log.debug("Buscando clientes por nombre de empresa: {}", companyName);
        List<ClientEntity> entities = ClientEntity.findByCompanyNameContaining(companyName);
        return mapper.toDomainList(entities);
    }

    @Override
    @Transactional
    public Client save(Client client) {
        log.debug("Guardando nuevo cliente: {}", client.getCompanyName());
        ClientEntity entity = mapper.toEntity(client);

        // Obtener el usuario
        if (client.getUserId() != null) {
            UserEntity userEntity = UserEntity.findById(client.getUserId());
            if (userEntity == null) {
                throw new ClientException("Usuario no encontrado con ID: " + client.getUserId());
            }
            entity.setUser(userEntity);
        }

        entity.persist();
        return mapper.toDomain(entity);
    }

    @Override
    @Transactional
    public Client update(Client client) {
        log.debug("Actualizando cliente con ID: {}", client.getClientId());
        ClientEntity entity = ClientEntity.findById(client.getClientId());

        if (entity != null) {
            mapper.updateEntityFromDomain(client, entity);

            // Actualizar usuario si ha cambiado
            if (client.getUserId() != null &&
                (entity.getUser() == null || !entity.getUser().getId().equals(client.getUserId()))) {
                UserEntity userEntity = UserEntity.findById(client.getUserId());
                if (userEntity == null) {
                    throw new ClientException("Usuario no encontrado con ID: " + client.getUserId());
                }
                entity.setUser(userEntity);
            }

            // Establecer updateDate
            entity.setUpdateDate(client.getUpdateDate());

            entity.persist();
            return mapper.toDomain(entity);
        } else {
            log.warn("No se encontró el cliente con ID: {}", client.getClientId());
            throw new jakarta.persistence.EntityNotFoundException("Cliente no encontrado con ID: " + client.getClientId());
        }
    }

    @Override
    @Transactional
    public boolean deleteById(Integer id) {
        log.debug("Eliminando cliente con ID: {}", id);
        return ClientEntity.deleteById(id);
    }

    @Override
    public boolean existsById(Integer id) {
        log.debug("Verificando existencia de cliente con ID: {}", id);
        return ClientEntity.existsById(id);
    }

    @Override
    public List<Client> findByBirthDateMonthAndDay(int month, int day) {
        log.debug("Buscando clientes con cumpleaños en mes {} y día {}", month, day);

        // Establecer explícitamente el tenant antes de la consulta
        TenantContext.setCurrentTenant("storeprocloud");

        try {
            // Usando EXTRACT que es más compatible con Hibernate/Panache
            List<ClientEntity> entities = ClientEntity.find(
                    "EXTRACT(MONTH FROM birthDate) = ?1 AND EXTRACT(DAY FROM birthDate) = ?2",
                    month, day).list();

            return mapper.toDomainList(entities);
        } finally {
            // Limpiar el contexto del tenant
            TenantContext.clear();
        }
    }

}