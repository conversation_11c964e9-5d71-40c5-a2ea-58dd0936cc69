package com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.mapper;

import com.ms.infinity.platform.customers.domain.model.Client;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity.ClientEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * Mapeador entre la entidad de persistencia ClientEntity y el modelo de dominio Client.
 * Utiliza MapStruct para automatizar la conversión entre ambos objetos.
 */
@Mapper(
        componentModel = "cdi",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface ClientMapper {

    /**
     * Convierte de entidad a modelo de dominio.
     *
     * @param entity Entidad de persistencia
     * @return Modelo de dominio
     */
    @Mapping(source = "user.id", target = "userId")
    Client toDomain(ClientEntity entity);

    /**
     * Convierte de modelo de dominio a entidad.
     *
     * @param domain Modelo de dominio
     * @return Entidad de persistencia
     */
    @Mapping(target = "user", ignore = true)
    ClientEntity toEntity(Client domain);

    /**
     * Convierte una lista de entidades a una lista de modelos de dominio.
     *
     * @param entities Lista de entidades
     * @return Lista de modelos de dominio
     */
    List<Client> toDomainList(List<ClientEntity> entities);

    /**
     * Actualiza una entidad existente con datos del modelo de dominio.
     *
     * @param domain Modelo de dominio con nuevos datos
     * @param entity Entidad a actualizar
     */
    @Mapping(target = "user", ignore = true)
    void updateEntityFromDomain(Client domain, @MappingTarget ClientEntity entity);
}