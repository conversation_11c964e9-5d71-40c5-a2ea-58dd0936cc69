package com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence;

import com.ms.infinity.platform.customers.application.port.output.UserRepositoryPort;
import com.ms.infinity.platform.customers.domain.model.UserDomain;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.mapper.UserMapper;
import com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity.UserEntity;
import com.ms.infinity.platform.customers.infrastructure.exception.ErrorCodes;
import com.ms.infinity.platform.customers.infrastructure.exception.UsuarioException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class UserRepositoryAdapter implements UserRepositoryPort {

    private static final Log LOG = LogFactory.getLog(UserRepositoryAdapter.class);
    private final UserMapper userMapper;

    public UserRepositoryAdapter(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @Override
    @Transactional
    public UserDomain save(UserDomain userDomain) {
        try {
            LOG.info("Domain received in repository: " + userDomain);
            UserEntity entity = userMapper.toEntity(userDomain);
            LOG.info("Entity created: " + entity);
            entity.persist();
            return userMapper.toDomain(entity);
        } catch (Exception e) {
            LOG.error("Error saving user: " + e.getMessage());
            throw e;
        }
    }

    @Override
    @Transactional
    public UserDomain update(UserDomain userDomain) {
        try {
            UserEntity existingEntity = UserEntity.findById(userDomain.getId());
            if (existingEntity == null) {
                throw new UsuarioException("User not found with ID: " + userDomain.getId());
            }

            // Update the existing entity with the new values
            userMapper.updateEntityFromDomain(userDomain, existingEntity);

            // Persist changes
            existingEntity.persist();

            // Return updated domain
            return userMapper.toDomain(existingEntity);
        } catch (Exception e) {
            LOG.error("Error updating user: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public Optional<UserDomain> findById(Long id) {
        UserEntity entity = UserEntity.findById(id);
        return Optional.ofNullable(entity).map(userMapper::toDomain);
    }


    @Override
    public Optional<UserDomain> findByEmail(String email) {
        UserEntity entity = UserEntity.find("email", email).firstResult();
        return Optional.ofNullable(entity).map(userMapper::toDomain);
    }

    @Override
    @Transactional
    public Optional<Boolean> deleteById(Long id) {
        try {
            // Primero verificamos si el usuario existe
            UserEntity userToDelete = UserEntity.findById(id);
            if (userToDelete == null) {
                LOG.warn("User not found with ID: " + id);
                return Optional.empty();
            }

            // Verificamos dependencias
            long dependentUsers = UserEntity.count("createdBy.id", id);
            if (dependentUsers > 0) {
                LOG.error("User has dependencies. Cannot delete user with ID: " + id);
                throw new UsuarioException(ErrorCodes.ERROR_USER_WITH_DEPENDENCIES.toString());
            }

            try {
                // Intentamos eliminar el usuario
                boolean deleted = UserEntity.deleteById(id);
                if (deleted) {
                    LOG.info("Successfully deleted user with ID: " + id);
                    return Optional.of(true);
                } else {
                    LOG.warn("Failed to delete user with ID: " + id);
                    return Optional.of(false);
                }
            } catch (Exception e) {
                LOG.error("Error during user deletion: " + e.getMessage(), e);
                return Optional.of(false);
            }
        } catch (UsuarioException e) {
            LOG.error("Business error during user deletion: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            LOG.error("Unexpected error during user deletion: " + e.getMessage(), e);
            throw new UsuarioException("Error while trying to delete user: " + e.getMessage());
        }
    }

    @Override
    public List<UserDomain> findAll(Boolean isActive) {
        if (isActive != null) {
            return UserEntity.<UserEntity>list("isActive", isActive).stream()
                    .map(userMapper::toDomain)
                    .toList();
        }
        return UserEntity.<UserEntity>listAll().stream()
                .map(userMapper::toDomain)
                .toList();
    }

}