package com.ms.infinity.platform.customers.infrastructure.adapter.output.persistence.entity;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Entidad JPA para la tabla clients.
 * Utiliza Panache para facilitar las operaciones de persistencia.
 */
@Entity
@Table(name = "clients")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientEntity extends PanacheEntityBase {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "client_id")
    private Integer clientId;

    @Column(name = "company_name", nullable = false)
    private String companyName;

    @Column(name = "tax_id", nullable = false)
    private String taxId;

    @Column(name = "business_name")
    private String businessName;

    @Column(name = "verification_number")
    private String verificationNumber;

    @Column(name = "contact_name")
    private String contactName;

    @Column(name = "address")
    private String address;

    @Column(name = "country_code")
    private String countryCode;

    @Column(name = "phone")
    private String phone;

    @Column(name = "email")
    private String email;

    @Column(name = "birth_date")
    private LocalDate birthDate;

    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @Column(name = "vat_rate", nullable = false)
    private BigDecimal vatRate;

    @Column(name = "consumption_tax_rate", nullable = false)
    private BigDecimal consumptionTaxRate;

    @Column(name = "allowed_users", nullable = false)
    private Integer allowedUsers;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    @ManyToOne
    @JoinColumn(name = "Userid", nullable = false)
    private UserEntity user;

    @Column(name = "updateDate")
    private LocalDateTime updateDate;

    /**
     * Método para encontrar clientes por nombre de empresa (búsqueda parcial).
     *
     * @param companyName Nombre o parte del nombre de la empresa
     * @return Lista de entidades que coinciden con el criterio
     */
    public static List<ClientEntity> findByCompanyNameContaining(String companyName) {
        return list("LOWER(companyName) LIKE LOWER(?1)", "%" + companyName + "%");
    }

    /**
     * Método para verificar si un cliente existe por su ID.
     *
     * @param id ID del cliente
     * @return true si existe, false si no existe
     */
    public static boolean existsById(Integer id) {
        return count("clientId = ?1", id) > 0;
    }
}