package com.ms.infinity.platform.customers.application.port.output;

import com.ms.infinity.platform.customers.domain.model.UserDomain;

import java.util.List;
import java.util.Optional;

/**
 * Interfaz que define las operaciones de persistencia
 * necesarias para el dominio de Usuario.
 */
public interface UserRepositoryPort {

    UserDomain save(UserDomain user);

    UserDomain update(UserDomain user);

    Optional<UserDomain> findById(Long id);

    Optional<UserDomain> findByEmail(String email);

    Optional<Boolean> deleteById(Long id);

    List<UserDomain> findAll(Boolean isActive);

}
